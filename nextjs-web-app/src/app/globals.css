@import "tailwindcss";
@tailwind base;
@tailwind components;
@tailwind utilities;

@theme {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

:root {
  --background: #ffffff;
  --foreground: #171717;
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
  --color-1: 239 246 255;  /* Light blue */
  --color-2: 199 210 254;  /* Blue */
  --color-3: 165 180 252;  /* Indigo */
  --color-4: 192 132 252;  /* Purple */
  --color-5: 251 207 232;  /* Pink */
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

/* Aurora animation */
@keyframes aurora {
  from {
    background-position: 50% 50%, 50% 50%;
  }
  to {
    background-position: 350% 50%, 350% 50%;
  }
}

.animate-aurora {
  animation: aurora 60s linear infinite;
}

/* Base styles */
html,
body {
  @apply antialiased;
  @apply bg-zinc-50 dark:bg-zinc-900;
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

/* Ensure proper scrolling */
html {
  height: auto;
  min-height: 100%;
}

body {
  height: auto;
  min-height: 100%;
}

/* Mobile optimizations */
@media (max-width: 640px) {
  html {
    font-size: 14px;
  }
}

/* Prevent horizontal scroll on mobile */
* {
  box-sizing: border-box;
}

/* Improve touch targets on mobile */
@media (max-width: 768px) {
  button,
  a,
  input,
  textarea {
    min-height: 44px;
  }
}

/* Ensure content is visible */
.relative {
  position: relative;
}

.z-10 {
  z-index: 10;
}
